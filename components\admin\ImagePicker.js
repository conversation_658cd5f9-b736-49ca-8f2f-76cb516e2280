import { useState, useEffect } from 'react';
import Image from 'next/image';
import styles from '@/styles/admin/ImagePicker.module.css';

/**
 * ImagePicker component for selecting images from available directories
 * Shows thumbnails of available images and allows selection
 */
export default function ImagePicker({ 
  onSelect, 
  onCancel, 
  currentImage = '', 
  directory = 'services',
  title = 'Select Image' 
}) {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedImage, setSelectedImage] = useState(currentImage);

  // Available service images (based on what we found in the public directory)
  const serviceImages = [
    '/images/services/airbrush-painting.jpeg',
    '/images/services/airbrush-temp.jpg',
    '/images/services/barber-cut.jpg',
    '/images/services/body-art-phot.jpg',
    '/images/services/braid-bar.jpg',
    '/images/services/face-paint.jpg',
    '/images/services/face-painting-children.jpg',
    '/images/services/facepaint-makup.jpg',
    '/images/services/festival-braids.jpg',
    '/images/services/glitter-bar.jpg',
    '/images/services/glitter-tattoo.jpeg',
    '/images/services/hair-braiding.jpg',
    '/images/services/kids-party.jpeg',
    '/images/services/uv-face.jpg',
    '/images/services/viking-festival.jpg',
    '/images/services/Braid artist.png'
  ];

  const productImages = [
    '/images/products/biodegradable-glitter.jpg',
    '/images/products/product-1.jpg',
    '/images/products/product-2.jpg',
    '/images/products/product-3.jpg',
    '/images/products/product-4.jpg',
    '/images/products/splitcake-aurora-pak.jpg',
    '/images/products/splitcake-aurora-prod.jpg',
    '/images/products/splitcake-cosmic-pak.jpg',
    '/images/products/splitcake-cosmic-prod.jpg',
    '/images/products/splitcake-flaimingtiger-pak.jpg',
    '/images/products/splitcake-flaimingtiger-prod.jpg',
    '/images/products/splitcake-forest-pak.jpg',
    '/images/products/splitcake-forest-prod.jpg',
    '/images/products/splitcake-galaxy-pak.jpg',
    '/images/products/splitcake-galaxy-prod.jpg',
    '/images/products/splitcake-neutral-pak.jpg',
    '/images/products/splitcake-neutral-prod.jpg',
    '/images/products/splitcake-ocean-pak.jpg',
    '/images/products/splitcake-ocean-prod.jpg',
    '/images/products/splitcake-pasteluv-pak.jpg',
    '/images/products/splitcake-pasteluv-prod.jpg',
    '/images/products/splitcake-pearl-pak.jpg',
    '/images/products/splitcake-pearl-prod.jpg',
    '/images/products/splitcake-rainbowuv-pak.jpg',
    '/images/products/splitcake-rainbowuv-prod.jpg',
    '/images/products/splitcake-tropical-pak.jpg',
    '/images/products/splitcake-tropical-prod.jpg',
    '/images/products/splitcake-uvliner-water activated-pak.jpg'
  ];

  useEffect(() => {
    // Set images based on directory
    const availableImages = directory === 'services' ? serviceImages : productImages;
    setImages(availableImages);
    setLoading(false);
  }, [directory]);

  const handleImageSelect = (imagePath) => {
    setSelectedImage(imagePath);
  };

  const handleConfirm = () => {
    onSelect(selectedImage);
  };

  const getImageName = (path) => {
    return path.split('/').pop().replace(/\.[^/.]+$/, "").replace(/-/g, ' ');
  };

  if (loading) {
    return (
      <div className={styles.modal}>
        <div className={styles.modalContent}>
          <div className={styles.loading}>Loading images...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.modal}>
        <div className={styles.modalContent}>
          <div className={styles.error}>{error}</div>
          <button onClick={onCancel} className={styles.cancelButton}>
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.modal}>
      <div className={styles.modalContent}>
        <div className={styles.header}>
          <h3>{title}</h3>
          <button onClick={onCancel} className={styles.closeButton}>
            ×
          </button>
        </div>

        <div className={styles.imageGrid}>
          {images.map((imagePath, index) => (
            <div
              key={index}
              className={`${styles.imageItem} ${
                selectedImage === imagePath ? styles.selected : ''
              }`}
              onClick={() => handleImageSelect(imagePath)}
            >
              <div className={styles.imageContainer}>
                <Image
                  src={imagePath}
                  alt={getImageName(imagePath)}
                  width={150}
                  height={150}
                  objectFit="cover"
                  className={styles.image}
                />
                <div className={styles.imageOverlay}>
                  <span className={styles.imageName}>
                    {getImageName(imagePath)}
                  </span>
                </div>
              </div>
              {selectedImage === imagePath && (
                <div className={styles.selectedIndicator}>✓</div>
              )}
            </div>
          ))}
        </div>

        <div className={styles.actions}>
          <button onClick={onCancel} className={styles.cancelButton}>
            Cancel
          </button>
          <button 
            onClick={handleConfirm} 
            className={styles.confirmButton}
            disabled={!selectedImage}
          >
            Select Image
          </button>
        </div>

        {selectedImage && (
          <div className={styles.preview}>
            <h4>Selected Image:</h4>
            <div className={styles.previewContainer}>
              <Image
                src={selectedImage}
                alt="Selected image"
                width={200}
                height={200}
                objectFit="cover"
                className={styles.previewImage}
              />
              <div className={styles.previewPath}>
                <strong>Path:</strong> {selectedImage}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
