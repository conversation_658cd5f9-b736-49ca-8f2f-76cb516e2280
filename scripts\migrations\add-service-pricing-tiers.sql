-- Migration: Add Service Pricing Tiers
-- This migration adds support for multiple pricing tiers per service
-- allowing services to have different durations and prices

-- Create service_pricing_tiers table
CREATE TABLE IF NOT EXISTS service_pricing_tiers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  service_id UUID NOT NULL REFERENCES services(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL, -- e.g., "Basic", "Standard", "Premium"
  description TEXT, -- Optional description of what's included
  duration INTEGER NOT NULL, -- Duration in minutes
  price DECIMAL(10,2) NOT NULL, -- Price for this tier
  is_default BOOLEAN DEFAULT false, -- Mark one tier as default
  sort_order INTEGER DEFAULT 0, -- For ordering tiers
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_service_pricing_tiers_service_id ON service_pricing_tiers(service_id);
CREATE INDEX IF NOT EXISTS idx_service_pricing_tiers_sort_order ON service_pricing_tiers(service_id, sort_order);

-- Add RLS policies for service_pricing_tiers
ALTER TABLE service_pricing_tiers ENABLE ROW LEVEL SECURITY;

-- Policy for public read access (for booking system)
CREATE POLICY "Public can read service pricing tiers" ON service_pricing_tiers
  FOR SELECT USING (true);

-- Policy for admin full access
CREATE POLICY "Admin can manage service pricing tiers" ON service_pricing_tiers
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_roles.id = auth.uid() 
      AND user_roles.role = 'admin'
    )
  );

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_service_pricing_tiers_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_service_pricing_tiers_updated_at
  BEFORE UPDATE ON service_pricing_tiers
  FOR EACH ROW
  EXECUTE FUNCTION update_service_pricing_tiers_updated_at();

-- Migrate existing services to have default pricing tiers
-- This creates a "Standard" tier for each existing service using their current price and duration
INSERT INTO service_pricing_tiers (service_id, name, description, duration, price, is_default, sort_order)
SELECT 
  id as service_id,
  'Standard' as name,
  'Standard service duration and pricing' as description,
  duration,
  price,
  true as is_default,
  1 as sort_order
FROM services
WHERE NOT EXISTS (
  SELECT 1 FROM service_pricing_tiers WHERE service_id = services.id
);

-- Add some example additional tiers for demonstration
-- These can be customized later through the admin interface

-- Add Basic tier (shorter duration, lower price) for services over 60 minutes
INSERT INTO service_pricing_tiers (service_id, name, description, duration, price, is_default, sort_order)
SELECT 
  id as service_id,
  'Basic' as name,
  'Shorter session with essential service' as description,
  GREATEST(30, duration - 30) as duration, -- At least 30 minutes, or 30 minutes less than standard
  ROUND(price * 0.7, 2) as price, -- 70% of standard price
  false as is_default,
  0 as sort_order
FROM services
WHERE duration > 60 -- Only for services longer than 60 minutes
AND NOT EXISTS (
  SELECT 1 FROM service_pricing_tiers 
  WHERE service_id = services.id AND name = 'Basic'
);

-- Add Premium tier (longer duration, higher price) for most services
INSERT INTO service_pricing_tiers (service_id, name, description, duration, price, is_default, sort_order)
SELECT 
  id as service_id,
  'Premium' as name,
  'Extended session with additional touches and detail' as description,
  duration + 30 as duration, -- 30 minutes longer
  ROUND(price * 1.4, 2) as price, -- 140% of standard price
  false as is_default,
  2 as sort_order
FROM services
WHERE NOT EXISTS (
  SELECT 1 FROM service_pricing_tiers 
  WHERE service_id = services.id AND name = 'Premium'
);

-- Create a view for easy querying of services with their pricing tiers
CREATE OR REPLACE VIEW services_with_pricing AS
SELECT 
  s.*,
  json_agg(
    json_build_object(
      'id', spt.id,
      'name', spt.name,
      'description', spt.description,
      'duration', spt.duration,
      'price', spt.price,
      'is_default', spt.is_default,
      'sort_order', spt.sort_order
    ) ORDER BY spt.sort_order, spt.name
  ) as pricing_tiers
FROM services s
LEFT JOIN service_pricing_tiers spt ON s.id = spt.service_id
GROUP BY s.id, s.name, s.description, s.duration, s.price, s.color, s.category, 
         s.image_url, s.gallery_images, s.status, s.featured, s.meta_title, 
         s.meta_description, s.booking_requirements, s.availability_notes, 
         s.created_at, s.updated_at;

-- Grant necessary permissions
GRANT SELECT ON services_with_pricing TO anon, authenticated;
GRANT ALL ON service_pricing_tiers TO authenticated;
GRANT USAGE ON SEQUENCE service_pricing_tiers_id_seq TO authenticated;
