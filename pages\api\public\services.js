import { supabase } from '@/lib/supabase';

/**
 * Public API endpoint for fetching services
 * This endpoint provides services data for the public website
 * No authentication required - only returns active services
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Fetch active services with pricing tiers from database
    const { data: services, error } = await supabase
      .from('services_with_pricing')
      .select('*')
      .eq('status', 'active')
      .order('name');

    if (error) {
      console.error('Error fetching services:', error);
      return res.status(500).json({ error: 'Failed to fetch services' });
    }

    // Transform database services to match the format expected by the frontend
    const transformedServices = services.map(service => ({
      id: service.id,
      title: service.name,
      description: service.description || '',
      image: service.image_url || '/images/services/face-paint.jpg',
      category: service.category || 'general',
      icon: getCategoryIcon(service.category),
      pricing: formatPricingTiers(service.pricing_tiers, service.price, service.duration),
      accentColor: service.color || '#4ECDC4',
      duration: service.duration,
      featured: service.featured || false,
      pricingTiers: service.pricing_tiers || []
    }));

    return res.status(200).json({ services: transformedServices });

  } catch (error) {
    console.error('Error in services API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Get category icon based on service category
 */
function getCategoryIcon(category) {
  const iconMap = {
    'painting': '🎨',
    'airbrush': '🎨',
    'braiding': '💇',
    'hair': '💇',
    'glitter': '✨',
    'sparkle': '✨',
    'special': '🎭',
    'uv': '🌟'
  };

  return iconMap[category] || '🎨';
}

/**
 * Format pricing tiers for display
 */
function formatPricingTiers(pricingTiers, fallbackPrice, fallbackDuration) {
  if (pricingTiers && pricingTiers.length > 0) {
    // Use pricing tiers from database
    return pricingTiers.map(tier => ({
      title: `${tier.name} (${tier.duration} min)`,
      price: `$${tier.price}`
    }));
  } else {
    // Fallback to original pricing format
    return formatPricing(fallbackPrice, fallbackDuration);
  }
}

/**
 * Format pricing information based on service price and duration
 */
function formatPricing(price, duration) {
  const basePrice = parseFloat(price) || 0;

  // Generate pricing tiers based on the base price
  return [
    {
      title: 'Individual service',
      price: `$${basePrice.toFixed(0)}`
    },
    {
      title: 'Group bookings (5+ people)',
      price: `from $${(basePrice * 0.85).toFixed(0)} per person`
    },
    {
      title: 'Event packages',
      price: 'Please contact for custom quotes'
    },
    {
      title: `Duration: ${duration} minutes`,
      price: ''
    }
  ];
}
