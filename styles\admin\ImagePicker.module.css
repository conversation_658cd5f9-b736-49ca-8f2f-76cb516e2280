/* Modal overlay */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

/* Modal content */
.modalContent {
  background: white;
  border-radius: 12px;
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Image grid */
.imageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  padding: 1.5rem;
  max-height: 400px;
  overflow-y: auto;
}

/* Image item */
.imageItem {
  position: relative;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.imageItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.imageItem.selected {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

/* Image container */
.imageContainer {
  position: relative;
  width: 100%;
  height: 150px;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.imageItem:hover .image {
  transform: scale(1.05);
}

/* Image overlay */
.imageOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 0.75rem 0.5rem 0.5rem;
  transform: translateY(100%);
  transition: transform 0.2s ease;
}

.imageItem:hover .imageOverlay {
  transform: translateY(0);
}

.imageName {
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Selected indicator */
.selectedIndicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #667eea;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Actions */
.actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.cancelButton,
.confirmButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.cancelButton {
  background: #6c757d;
  color: white;
}

.cancelButton:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.confirmButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.confirmButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.confirmButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Preview section */
.preview {
  padding: 1.5rem;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.preview h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
}

.previewContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.previewImage {
  border-radius: 8px;
  border: 1px solid #ddd;
}

.previewPath {
  flex: 1;
  font-size: 0.9rem;
  color: #666;
  word-break: break-all;
}

.previewPath strong {
  color: #333;
}

/* Loading and error states */
.loading,
.error {
  padding: 2rem;
  text-align: center;
  color: #666;
}

.error {
  color: #dc3545;
}

/* Responsive design */
@media (max-width: 768px) {
  .modal {
    padding: 0.5rem;
  }

  .modalContent {
    width: 100%;
    max-height: 95vh;
  }

  .imageGrid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
    padding: 1rem;
  }

  .imageContainer {
    height: 120px;
  }

  .header {
    padding: 1rem;
  }

  .actions {
    padding: 1rem;
    flex-direction: column;
  }

  .cancelButton,
  .confirmButton {
    width: 100%;
  }

  .previewContainer {
    flex-direction: column;
    align-items: flex-start;
  }

  .previewImage {
    width: 100%;
    max-width: 200px;
  }
}

/* Scrollbar styling */
.imageGrid::-webkit-scrollbar {
  width: 8px;
}

.imageGrid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.imageGrid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.imageGrid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
