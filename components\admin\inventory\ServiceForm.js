import { useState, useEffect } from 'react';
import Image from 'next/image';
import ImagePicker from '@/components/admin/ImagePicker';
import styles from '@/styles/admin/ServiceForm.module.css';

export default function ServiceForm({ service, onSave, onCancel }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [showImagePicker, setShowImagePicker] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    duration: 60, // in minutes
    price: '',
    color: '#6a0dad',
    category: '',
    image_url: '',
    gallery_images: [],
    status: 'active',
    featured: false,
    meta_title: '',
    meta_description: '',
    booking_requirements: '',
    availability_notes: ''
  });

  // Initialize form data when service prop changes
  useEffect(() => {
    if (service) {
      setFormData({
        name: service.name || '',
        description: service.description || '',
        duration: service.duration || 60,
        price: service.price || '',
        color: service.color || '#6a0dad',
        category: service.category || '',
        image_url: service.image_url || '',
        gallery_images: service.gallery_images || [],
        status: service.status || 'active',
        featured: service.featured || false,
        meta_title: service.meta_title || '',
        meta_description: service.meta_description || '',
        booking_requirements: service.booking_requirements || '',
        availability_notes: service.availability_notes || ''
      });
    }
  }, [service]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear any previous errors
    if (error) setError(null);
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError('Service name is required');
      return false;
    }
    if (!formData.price || parseFloat(formData.price) <= 0) {
      setError('Valid price is required');
      return false;
    }
    if (!formData.duration || parseInt(formData.duration) <= 0) {
      setError('Valid duration is required');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Prepare data for API
      const serviceData = {
        ...formData,
        price: parseFloat(formData.price),
        duration: parseInt(formData.duration, 10)
      };

      // Create or update service
      const url = service ? `/api/admin/services/${service.id}` : '/api/admin/services';
      const method = service ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(serviceData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save service');
      }

      setSuccess(true);
      setTimeout(() => {
        onSave && onSave();
      }, 1000);

    } catch (error) {
      console.error('Error saving service:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = async (file) => {
    // TODO: Implement image upload functionality
    console.log('Image upload not yet implemented:', file);
  };

  return (
    <div className={styles.serviceForm}>
      <div className={styles.formHeader}>
        <h2>{service ? 'Edit Service' : 'Add New Service'}</h2>
      </div>

      {error && (
        <div className={styles.error}>
          {error}
        </div>
      )}

      {success && (
        <div className={styles.success}>
          Service saved successfully!
        </div>
      )}

      <form onSubmit={handleSubmit} className={styles.form}>
        {/* Basic Information */}
        <div className={styles.section}>
          <h3>Basic Information</h3>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="name">Service Name *</label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={styles.input}
                required
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="category">Category</label>
              <select
                id="category"
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                className={styles.select}
              >
                <option value="">Select Category</option>
                <option value="painting">Face & Body Painting</option>
                <option value="airbrush">Airbrush Services</option>
                <option value="braiding">Hair Braiding</option>
                <option value="glitter">Glitter Services</option>
                <option value="special">Special Events</option>
              </select>
            </div>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className={styles.textarea}
              rows={4}
              placeholder="Describe the service, what's included, and any special features..."
            />
          </div>
        </div>

        {/* Pricing and Duration */}
        <div className={styles.section}>
          <h3>Pricing & Duration</h3>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="price">Price (AUD) *</label>
              <input
                type="number"
                id="price"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                className={styles.input}
                required
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="duration">Duration (minutes) *</label>
              <input
                type="number"
                id="duration"
                min="1"
                value={formData.duration}
                onChange={(e) => handleInputChange('duration', e.target.value)}
                className={styles.input}
                required
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="color">Calendar Color</label>
              <input
                type="color"
                id="color"
                value={formData.color}
                onChange={(e) => handleInputChange('color', e.target.value)}
                className={styles.colorInput}
              />
            </div>
          </div>
        </div>

        {/* Images */}
        <div className={styles.section}>
          <h3>Images</h3>

          <div className={styles.formGroup}>
            <label htmlFor="image_url">Main Image</label>

            {formData.image_url ? (
              <div className={styles.imagePreviewContainer}>
                <div className={styles.imagePreview}>
                  <Image
                    src={formData.image_url}
                    alt="Service image"
                    width={200}
                    height={150}
                    objectFit="cover"
                    className={styles.previewImage}
                  />
                  <div className={styles.imageActions}>
                    <button
                      type="button"
                      onClick={() => setShowImagePicker(true)}
                      className={styles.changeImageButton}
                    >
                      Change Image
                    </button>
                    <button
                      type="button"
                      onClick={() => handleInputChange('image_url', '')}
                      className={styles.removeImageButton}
                    >
                      Remove
                    </button>
                  </div>
                </div>
                <div className={styles.imagePath}>
                  <strong>Path:</strong> {formData.image_url}
                </div>
              </div>
            ) : (
              <div className={styles.noImageContainer}>
                <div className={styles.noImagePlaceholder}>
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                    <circle cx="8.5" cy="8.5" r="1.5"/>
                    <polyline points="21,15 16,10 5,21"/>
                  </svg>
                  <p>No image selected</p>
                </div>
                <button
                  type="button"
                  onClick={() => setShowImagePicker(true)}
                  className={styles.selectImageButton}
                >
                  Select Image
                </button>
              </div>
            )}

            <div className={styles.manualInputOption}>
              <details>
                <summary>Or enter image URL manually</summary>
                <input
                  type="url"
                  id="image_url"
                  value={formData.image_url}
                  onChange={(e) => handleInputChange('image_url', e.target.value)}
                  className={styles.input}
                  placeholder="https://example.com/image.jpg or /images/services/image.jpg"
                />
              </details>
            </div>
          </div>
        </div>

        {/* Settings */}
        <div className={styles.section}>
          <h3>Settings</h3>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="status">Status</label>
              <select
                id="status"
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
                className={styles.select}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
            <div className={styles.formGroup}>
              <label className={styles.checkboxLabel}>
                <input
                  type="checkbox"
                  checked={formData.featured}
                  onChange={(e) => handleInputChange('featured', e.target.checked)}
                />
                Featured Service
              </label>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className={styles.formActions}>
          <button
            type="button"
            onClick={onCancel}
            className={styles.cancelButton}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.saveButton}
            disabled={loading}
          >
            {loading ? 'Saving...' : (service ? 'Update Service' : 'Create Service')}
          </button>
        </div>
      </form>

      {/* Image Picker Modal */}
      {showImagePicker && (
        <ImagePicker
          onSelect={(imagePath) => {
            handleInputChange('image_url', imagePath);
            setShowImagePicker(false);
          }}
          onCancel={() => setShowImagePicker(false)}
          currentImage={formData.image_url}
          directory="services"
          title="Select Service Image"
        />
      )}
    </div>
  );
}
